package com.ruoyi.system.service.impl;

import java.util.List;
import java.util.Date;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.SysFileMapper;
import com.ruoyi.system.domain.SysFile;
import com.ruoyi.system.service.ISysFileService;

/**
 * 文件管理Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class SysFileServiceImpl implements ISysFileService 
{
    @Autowired
    private SysFileMapper sysFileMapper;

    /**
     * 查询文件信息
     * 
     * @param id 文件主键
     * @return 文件信息
     */
    @Override
    public SysFile selectSysFileById(Long id)
    {
        return sysFileMapper.selectSysFileById(id);
    }

    /**
     * 查询文件信息列表
     * 
     * @param sysFile 文件信息
     * @return 文件信息
     */
    @Override
    public List<SysFile> selectSysFileList(SysFile sysFile)
    {
        return sysFileMapper.selectSysFileList(sysFile);
    }

    /**
     * 新增文件信息
     * 
     * @param sysFile 文件信息
     * @return 结果
     */
    @Override
    public int insertSysFile(SysFile sysFile)
    {
        if (sysFile.getCreatedAt() == null) {
            sysFile.setCreatedAt(new Date());
        }
        if (sysFile.getIsDelete() == null) {
            sysFile.setIsDelete(0);
        }
        return sysFileMapper.insertSysFile(sysFile);
    }

    /**
     * 修改文件信息
     * 
     * @param sysFile 文件信息
     * @return 结果
     */
    @Override
    public int updateSysFile(SysFile sysFile)
    {
        return sysFileMapper.updateSysFile(sysFile);
    }

    /**
     * 批量删除文件信息
     * 
     * @param ids 需要删除的文件主键
     * @return 结果
     */
    @Override
    public int deleteSysFileByIds(Long[] ids)
    {
        return sysFileMapper.deleteSysFileByIds(ids);
    }

    /**
     * 删除文件信息
     * 
     * @param id 文件主键
     * @return 结果
     */
    @Override
    public int deleteSysFileById(Long id)
    {
        return sysFileMapper.deleteSysFileById(id);
    }

    /**
     * 根据关联表和关联ID查询文件列表
     * 
     * @param relatedTable 关联表名
     * @param relatedId 关联表ID
     * @return 文件信息集合
     */
    @Override
    public List<SysFile> selectSysFileByRelated(String relatedTable, Long relatedId)
    {
        return sysFileMapper.selectSysFileByRelated(relatedTable, relatedId);
    }

    /**
     * 根据存储路径查询文件信息
     * 
     * @param path 存储路径
     * @return 文件信息
     */
    @Override
    public SysFile selectSysFileByPath(String path)
    {
        return sysFileMapper.selectSysFileByPath(path);
    }
}

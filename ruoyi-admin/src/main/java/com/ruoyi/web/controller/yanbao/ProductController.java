package com.ruoyi.web.controller.yanbao;

import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.yanbao.entity.Product;
import com.ruoyi.yanbao.entity.Store;
import com.ruoyi.yanbao.entity.vo.ProductVo;
import com.ruoyi.yanbao.service.ProductService;
import com.ruoyi.yanbao.service.StoreService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/yanbao/product")
public class ProductController extends BaseController {

    @Autowired
    private ProductService productService;

    @Autowired
    private StoreService storeService;

    /**
     * 查下当前客户可用的保险产品
     */
    @GetMapping("/getAvailableProducts")
    public AjaxResult getAvailableProducts() {
        return AjaxResult.success(productService.getAvailableProducts(getLoginUser().getUser().getStoreIds()));
    }

    @GetMapping("/getProductInfo")
    public AjaxResult getProductInfo(Long productId) {
        ProductVo product = productService.getProductInfo(productId);
        if (product == null) {
            return AjaxResult.error("产品不存在");
        }
        return AjaxResult.success(product);
    }

    /**
     * 获取产品适用门店
     *
     * @param productId
     * @return
     */
    @GetMapping("/getProductSuitStore")
    public AjaxResult getProductSuitStore(Long productId) {
        String storeIds = getLoginUser().getUser().getStoreIds();
        List<Store> stores = new ArrayList<>();
        if (StringUtils.isNotEmpty(storeIds)) {
            stores = storeService.listByIds(Arrays.asList(storeIds.split(",")));
        } else {
            stores = storeService.selectStoreAll(Constants.STATUS_ENABLE);
        }
        stores = stores.stream().filter(s -> s.getStatus() == Constants.STATUS_ENABLE).collect(Collectors.toList());
        if (productId != null) {
            Product product = productService.getById(productId);
            if (product != null && StringUtils.isNotEmpty(product.getSuitStoreIds())) {
                List<String> storeIdList = Arrays.asList(product.getSuitStoreIds().split(","));
                stores = stores.stream().filter(s -> storeIdList.contains(s.getId().toString())).collect(Collectors.toList());
            }
        }
        return AjaxResult.success(stores);
    }
}

package com.ruoyi.yanbao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.yanbao.entity.Product;
import com.ruoyi.yanbao.entity.vo.ProductVo;

import java.util.List;

/**
 * <p>
 * 产品表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
public interface ProductService extends IService<Product> {

    /**
     * 查询当前客户可用的保险产品
     *
     * @param storeIds 经销商ID列表
     * @return 产品列表
     */
    List<Product> getAvailableProducts(String storeIds);

    /**
     * 查询产品详情
     *
     * @param productId
     * @return
     */
    ProductVo getProductInfo(Long productId);
}

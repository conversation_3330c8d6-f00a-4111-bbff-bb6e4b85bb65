package com.ruoyi.yanbao.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.yanbao.entity.Product;
import com.ruoyi.yanbao.entity.ProductTerm;
import com.ruoyi.yanbao.entity.vo.ProductVo;
import com.ruoyi.yanbao.mapper.ProductMapper;
import com.ruoyi.yanbao.mapper.ProductTermMapper;
import com.ruoyi.yanbao.service.ProductService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 产品表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Service
public class ProductServiceImpl extends ServiceImpl<ProductMapper, Product> implements ProductService {

    @Autowired
    private ProductTermMapper productTermMapper;

    @Override
    public List<Product> getAvailableProducts(String storeIds) {
        LambdaQueryWrapper<Product> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Product::getStatus, Constants.STATUS_ENABLE);
        queryWrapper.select(Product::getId, Product::getName, Product::getRemark, Product::getSuitStoreIds,
                Product::getType);
        queryWrapper.orderByDesc(Product::getSort);
        List<Product> products = list(queryWrapper);
        if (StringUtils.isNotEmpty(storeIds)) {
            List<String> storeIdList = Arrays.asList(storeIds.split(","));
            products = products.stream().filter(p -> {
                if (StringUtils.isNotEmpty(p.getSuitStoreIds())) {
                    String[] storeIdArray = p.getSuitStoreIds().split(",");
                    for (String storeId : storeIdArray) {
                        if (storeIdList.contains(storeId)) {
                            return true;
                        }
                    }
                    return false;
                }
                return true;
            }).collect(Collectors.toList());
        }
        return products;
    }


    @Override
    public ProductVo getProductInfo(Long productId) {
        if (productId == null) {
            return null;
        }
        Product product = getById(productId);
        if (product == null || !Constants.STATUS_ENABLE.equals(product.getStatus())) {
            return null;
        }
        ProductVo productVo = new ProductVo();
        productVo.setId(product.getId());
        productVo.setName(product.getName());
        productVo.setRemark(product.getRemark());
        productVo.setType(product.getType());
        List<ProductTerm> productTerms =
                productTermMapper.selectList(new LambdaQueryWrapper<ProductTerm>().eq(ProductTerm::getProductId, productId));
        productVo.setTerms(productTerms);
        return productVo;
    }
}
